import { z } from "zod";

export const createProductSchema = z.object({
  itemName: z.string().min(1, "Item name is required").max(255),
  slug: z.string().min(1, "Slug is required").max(255).regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  description: z.string().optional(),
  sellType: z.enum(["auction", "buy-now"]),
  priceUSD: z.number().positive("Price must be positive"),
  categoryId: z.string().uuid("Invalid category ID"),
  itemTypeId: z.string().uuid("Invalid item type ID"),
  
  // Auction specific fields
  auctionStartDate: z.string().refine((val) => {
    if (!val) return true;
    // Allow both "YYYY-MM-DDTHH:mm" and "YYYY-MM-DDTHH:mm:ss" formats
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?$/;
    if (!dateTimeRegex.test(val)) return false;
    // Ensure it's a valid date
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid datetime format").optional(),
  auctionEndDate: z.string().refine((val) => {
    if (!val) return true;
    // Allow both "YYYY-MM-DDTHH:mm" and "YYYY-MM-DDTHH:mm:ss" formats
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?$/;
    if (!dateTimeRegex.test(val)) return false;
    // Ensure it's a valid date
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid datetime format").optional(),
  
  // Extended bidding settings
  extendedBiddingEnabled: z.boolean().default(false),
  extendedBiddingMinutes: z.number().int().min(1).max(60).optional(),
  extendedBiddingDuration: z.number().int().min(1).max(120).optional(),
  
  // Images
  images: z.array(z.object({
    imageUrl: z.string().url(),
    altText: z.string().optional(),
    sortOrder: z.number().int().min(0).default(0),
    isMain: z.boolean().default(false)
  })).min(1, "At least one image is required").max(10, "Maximum 10 images allowed")
}).refine((data) => {
  // If auction type, start and end dates are required
  if (data.sellType === "auction") {
    return data.auctionStartDate && data.auctionEndDate;
  }
  return true;
}, {
  message: "Auction start and end dates are required for auction type",
  path: ["auctionStartDate"]
}).refine((data) => {
  // If extended bidding is enabled, minutes and duration are required
  if (data.extendedBiddingEnabled) {
    return data.extendedBiddingMinutes && data.extendedBiddingDuration;
  }
  return true;
}, {
  message: "Extended bidding minutes and duration are required when extended bidding is enabled",
  path: ["extendedBiddingMinutes"]
}).refine((data) => {
  // Auction end date must be after start date
  if (data.sellType === "auction" && data.auctionStartDate && data.auctionEndDate) {
    return new Date(data.auctionEndDate) > new Date(data.auctionStartDate);
  }
  return true;
}, {
  message: "Auction end date must be after start date",
  path: ["auctionEndDate"]
});

export const updateProductSchema = z.object({
  itemName: z.string().min(1, "Item name is required").max(255).optional(),
  slug: z.string().min(1, "Slug is required").max(255).regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens").optional(),
  description: z.string().optional(),
  sellType: z.enum(["auction", "buy-now"]).optional(),
  priceUSD: z.number().positive("Price must be positive").optional(),
  categoryId: z.string().uuid("Invalid category ID").optional(),
  itemTypeId: z.string().uuid("Invalid item type ID").optional(),

  // Auction specific fields
  auctionStartDate: z.string().refine((val) => {
    if (!val) return true;
    // Allow both "YYYY-MM-DDTHH:mm" and "YYYY-MM-DDTHH:mm:ss" formats
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?$/;
    if (!dateTimeRegex.test(val)) return false;
    // Ensure it's a valid date
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid datetime format").optional(),
  auctionEndDate: z.string().refine((val) => {
    if (!val) return true;
    // Allow both "YYYY-MM-DDTHH:mm" and "YYYY-MM-DDTHH:mm:ss" formats
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(:\d{2})?$/;
    if (!dateTimeRegex.test(val)) return false;
    // Ensure it's a valid date
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid datetime format").optional(),

  // Extended bidding settings
  extendedBiddingEnabled: z.boolean().optional(),
  extendedBiddingMinutes: z.number().int().min(1).max(60).optional(),
  extendedBiddingDuration: z.number().int().min(1).max(120).optional(),

  // Images
  images: z.array(z.object({
    imageUrl: z.string().url(),
    altText: z.string().optional(),
    sortOrder: z.number().int().min(0).default(0),
    isMain: z.boolean().default(false)
  })).max(10, "Maximum 10 images allowed").optional()
});

export const productQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(Number).pipe(z.number().int().min(1).max(100)).default("10"),
  sellType: z.enum(["auction", "buy-now"]).optional(),
  categoryId: z.string().uuid().optional(),
  itemTypeId: z.string().uuid().optional(),
  status: z.enum(["draft", "active", "sold", "cancelled"]).optional(),
  search: z.string().optional(),
  sortBy: z.enum(["createdAt", "priceUSD", "auctionEndDate", "bidCount"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
});

export const categorySchema = z.object({
  name: z.string().min(1, "Category name is required").max(100),
  description: z.string().optional()
});

export const itemTypeSchema = z.object({
  name: z.string().min(1, "Item type name is required").max(100),
  description: z.string().optional(),
  categoryId: z.string().uuid("Invalid category ID").optional()
});

export const bidSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  amount: z.number().positive("Bid amount must be positive")
});

export const imageUploadSchema = z.object({
  images: z.array(z.object({
    fieldname: z.string(),
    originalname: z.string(),
    encoding: z.string(),
    mimetype: z.string().refine(
      (type) => ["image/jpeg", "image/jpg", "image/png", "image/webp"].includes(type),
      "Only JPEG, PNG, and WebP images are allowed"
    ),
    size: z.number().max(5 * 1024 * 1024, "Image size must be less than 5MB"),
    buffer: z.instanceof(Buffer)
  })).min(1, "At least one image is required").max(10, "Maximum 10 images allowed")
});

export type CreateProductInput = z.infer<typeof createProductSchema>;
export type UpdateProductInput = z.infer<typeof updateProductSchema>;
export type ProductQueryInput = z.infer<typeof productQuerySchema>;
export type CategoryInput = z.infer<typeof categorySchema>;
export type ItemTypeInput = z.infer<typeof itemTypeSchema>;
export type BidInput = z.infer<typeof bidSchema>;
export type ImageUploadInput = z.infer<typeof imageUploadSchema>;
