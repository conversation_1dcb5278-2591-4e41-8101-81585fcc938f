import { Context, Next } from 'hono';
import multer from 'multer';
import { v2 as cloudinary } from 'cloudinary';
import { Readable } from 'stream';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Configure multer for memory storage
const storage = multer.memoryStorage();

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 10, // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only JPEG, PNG, and WebP images are allowed'));
    }
  },
});

// Helper function to upload buffer to Cloudinary
const uploadToCloudinary = (buffer: Buffer, filename: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        resource_type: 'image',
        folder: 'king-collectibles/products',
        public_id: `${Date.now()}-${filename}`,
        transformation: [
          { width: 1200, height: 1200, crop: 'limit', quality: 'auto' },
          { format: 'webp' }
        ]
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      }
    );

    const stream = Readable.from(buffer);
    stream.pipe(uploadStream);
  });
};

// Middleware to handle file uploads
export const uploadMiddleware = (fieldName: string = 'images') => {
  return async (c: Context, next: Next) => {
    try {
      const uploadHandler = upload.array(fieldName, 10);
      
      // Convert Hono request to Express-like request for multer
      const req = c.req.raw as any;
      const res = {} as any;

      await new Promise<void>((resolve, reject) => {
        uploadHandler(req, res, (err: any) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });

      console.log(req.body)

      // Store files in context for controller access
      c.set('files', req.files || []);
      
      await next();
    } catch (error) {
      console.error('Upload middleware error:', error);
      return c.json(
        {
          status: false,
          message: error instanceof Error ? error.message : 'File upload failed',
        },
        400
      );
    }
  };
};

// Service function to upload images to Cloudinary
export const uploadImagesToCloudinary = async (files: Express.Multer.File[]): Promise<string[]> => {
  try {
    const uploadPromises = files.map(async (file) => {
      const result = await uploadToCloudinary(file.buffer, file.originalname);
      return result.secure_url;
    });

    const urls = await Promise.all(uploadPromises);
    return urls;
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload images to cloud storage');
  }
};

// Alternative: Local file storage (for development)
export const saveFilesLocally = async (files: Express.Multer.File[]): Promise<string[]> => {
  const fs = await import('fs/promises');
  const path = await import('path');
  
  const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'products');
  
  // Ensure upload directory exists
  try {
    await fs.access(uploadDir);
  } catch {
    await fs.mkdir(uploadDir, { recursive: true });
  }

  const urls: string[] = [];
  
  for (const file of files) {
    const filename = `${Date.now()}-${file.originalname}`;
    const filepath = path.join(uploadDir, filename);
    
    await fs.writeFile(filepath, file.buffer);
    urls.push(`/uploads/products/${filename}`);
  }
  
  return urls;
};
