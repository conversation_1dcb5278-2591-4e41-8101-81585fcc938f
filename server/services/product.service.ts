import { prisma } from "../db";
import { errorResponse, successResponse } from "../utils/response.util";
import { 
  CreateProductInput, 
  UpdateProductInput, 
  ProductQueryInput,
  CategoryInput,
  ItemTypeInput,
  BidInput
} from "../schemas/product.schema";

class ProductService {
  async createProduct(data: CreateProductInput, sellerId: string) {
    // try {
      const { images, ...productData } = data;

      // Create product with images in a transaction
      const product = await prisma.$transaction(async (tx) => {
        // Create the product
        const newProduct = await tx.product.create({
          data: {
            ...productData,
            sellerId,
            status: "draft"
          }
        });

        // Create product images
        if (images && images.length > 0) {
          await tx.productImage.createMany({
            data: images.map((image, index) => ({
              productId: newProduct.id,
              imageUrl: image.imageUrl,
              altText: image.altText || `Product image ${index + 1}`,
              sortOrder: image.sortOrder || index,
              isMain: image.isMain || index === 0
            }))
          });
        }

        // Return product with images
        return await tx.product.findUnique({
          where: { id: newProduct.id },
          include: {
            images: {
              orderBy: { sortOrder: 'asc' }
            },
            category: true,
            itemType: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        });
      });

      return successResponse("Product created successfully", product);
    // } catch (error) {
    //   console.error("Create product error:", error);
    //   return errorResponse("Failed to create product");
    // }
  }

  async getProducts(query: ProductQueryInput) {
    try {
      const { page, limit, sellType, categoryId, itemTypeId, status, search, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};
      
      if (sellType) where.sellType = sellType;
      if (categoryId) where.categoryId = categoryId;
      if (itemTypeId) where.itemTypeId = itemTypeId;
      if (status) where.status = status;
      if (search) {
        where.OR = [
          { itemName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      // Build order by clause
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            images: {
              orderBy: { sortOrder: 'asc' }
            },
            category: true,
            itemType: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            },
            _count: {
              select: { bids: true }
            }
          }
        }),
        prisma.product.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return successResponse("Products retrieved successfully", {
        products,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    } catch (error) {
      console.error("Get products error:", error);
      return errorResponse("Failed to retrieve products");
    }
  }

  async getProductById(id: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          images: {
            orderBy: { sortOrder: 'asc' }
          },
          category: true,
          itemType: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          bids: {
            orderBy: { createdAt: 'desc' },
            take: 10,
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          _count: {
            select: { bids: true }
          }
        }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      return successResponse("Product retrieved successfully", product);
    } catch (error) {
      console.error("Get product error:", error);
      return errorResponse("Failed to retrieve product");
    }
  }

  async updateProduct(id: string, data: UpdateProductInput, sellerId: string) {
    try {
      // Check if product exists and belongs to seller
      const existingProduct = await prisma.product.findFirst({
        where: { id, sellerId }
      });

      if (!existingProduct) {
        return errorResponse("Product not found or unauthorized");
      }

      const { images, ...productData } = data;

      const product = await prisma.$transaction(async (tx) => {
        // Update product
        const updatedProduct = await tx.product.update({
          where: { id },
          data: productData
        });

        // Update images if provided
        if (images) {
          // Delete existing images
          await tx.productImage.deleteMany({
            where: { productId: id }
          });

          // Create new images
          if (images.length > 0) {
            await tx.productImage.createMany({
              data: images.map((image, index) => ({
                productId: id,
                imageUrl: image.imageUrl,
                altText: image.altText || `Product image ${index + 1}`,
                sortOrder: image.sortOrder || index,
                isMain: image.isMain || index === 0
              }))
            });
          }
        }

        return await tx.product.findUnique({
          where: { id },
          include: {
            images: {
              orderBy: { sortOrder: 'asc' }
            },
            category: true,
            itemType: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        });
      });

      return successResponse("Product updated successfully", product);
    } catch (error) {
      console.error("Update product error:", error);
      return errorResponse("Failed to update product");
    }
  }

  async deleteProduct(id: string, sellerId: string) {
    try {
      const product = await prisma.product.findFirst({
        where: { id, sellerId }
      });

      if (!product) {
        return errorResponse("Product not found or unauthorized");
      }

      await prisma.product.delete({
        where: { id }
      });

      return successResponse("Product deleted successfully");
    } catch (error) {
      console.error("Delete product error:", error);
      return errorResponse("Failed to delete product");
    }
  }

  async getCategories() {
    try {
      const categories = await prisma.category.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' },
        include: {
          _count: {
            select: { products: true }
          }
        }
      });

      return successResponse("Categories retrieved successfully", categories);
    } catch (error) {
      console.error("Get categories error:", error);
      return errorResponse("Failed to retrieve categories");
    }
  }

  async createCategory(data: CategoryInput) {
    try {
      const category = await prisma.category.create({
        data
      });

      return successResponse("Category created successfully", category);
    } catch (error) {
      console.error("Create category error:", error);
      return errorResponse("Failed to create category");
    }
  }

  async getItemTypes(categoryId?: string) {
    try {
      const where: any = { isActive: true };
      if (categoryId) where.categoryId = categoryId;

      const itemTypes = await prisma.itemType.findMany({
        where,
        orderBy: { name: 'asc' },
        include: {
          category: true,
          _count: {
            select: { products: true }
          }
        }
      });

      return successResponse("Item types retrieved successfully", itemTypes);
    } catch (error) {
      console.error("Get item types error:", error);
      return errorResponse("Failed to retrieve item types");
    }
  }

  async createItemType(data: ItemTypeInput) {
    try {
      const itemType = await prisma.itemType.create({
        data
      });

      return successResponse("Item type created successfully", itemType);
    } catch (error) {
      console.error("Create item type error:", error);
      return errorResponse("Failed to create item type");
    }
  }

  async placeBid(data: BidInput, bidderId: string) {
    try {
      const { productId, amount } = data;

      // Get product with current highest bid
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          bids: {
            orderBy: { amount: 'desc' },
            take: 1
          }
        }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      if (product.sellType !== 'auction') {
        return errorResponse("This product is not an auction");
      }

      if (product.status !== 'active') {
        return errorResponse("Auction is not active");
      }

      // Check if auction has ended
      if (product.auctionEndDate && new Date() > new Date(product.auctionEndDate)) {
        return errorResponse("Auction has ended");
      }

      // Check if bid is higher than current highest bid
      const currentHighestBid = product.bids[0]?.amount || product.priceUSD;
      const currentHighestBidNumber = typeof currentHighestBid === 'number' ? currentHighestBid : Number(currentHighestBid);
      if (amount <= currentHighestBidNumber) {
        return errorResponse(`Bid must be higher than current highest bid of $${currentHighestBidNumber}`);
      }

      // Create bid in transaction
      const result = await prisma.$transaction(async (tx) => {
        // Mark all previous bids as not winning
        await tx.bid.updateMany({
          where: { productId },
          data: { isWinning: false }
        });

        // Create new bid
        const bid = await tx.bid.create({
          data: {
            productId,
            bidderId,
            amount,
            isWinning: true
          },
          include: {
            bidder: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        });

        // Update product with new current bid and bid count
        await tx.product.update({
          where: { id: productId },
          data: {
            currentBid: amount,
            bidCount: { increment: 1 }
          }
        });

        return bid;
      });

      return successResponse("Bid placed successfully", result);
    } catch (error) {
      console.error("Place bid error:", error);
      return errorResponse("Failed to place bid");
    }
  }
}

export default new ProductService();
