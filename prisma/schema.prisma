// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/client"
  engineType      = "binary"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(uuid())
  userId        String?   @unique
  firstName     String?
  lastName      String?
  email         String?   @unique
  emailVerified DateTime?
  phoneNumber   String?   @unique
  phoneVerified DateTime?
  role          String   @default("user")
  oauthProvider String?
  oauthId       String?
  googleId      String?   @unique
  password      String?
  refreshToken  String?   @db.Text
  image         String?
  isActive      Boolean  @default(true)
  isEmailVerified Boolean @default(false)
  isPhoneVerified Boolean @default(false)
  isBlocked     Boolean  @default(false)
  country       String?
  ipAddress     String?
  lastLogin     DateTime?
  lastLoginIp   String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  products      Product[]
  bids          Bid[]
}

model Category {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products    Product[]
  itemTypes   ItemType[]
}

model ItemType {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  categoryId  String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  category    Category? @relation(fields: [categoryId], references: [id])
  products    Product[]

  @@index([categoryId])
}

model Product {
  id              String      @id @default(uuid())
  itemName        String
  slug            String?     @unique
  description     String?     @db.Text
  sellType        String      // 'auction' or 'buy-now'
  priceUSD        Decimal     @db.Decimal(10, 2)

  // Auction specific fields
  auctionStartDate DateTime?
  auctionEndDate   DateTime?
  currentBid       Decimal?    @db.Decimal(10, 2)
  bidCount         Int         @default(0)

  // Extended bidding settings
  extendedBiddingEnabled Boolean @default(false)
  extendedBiddingMinutes Int?    // Minutes before end to trigger extension
  extendedBiddingDuration Int?   // Duration of extension in minutes

  // Status and metadata
  status          String      @default("draft") // draft, active, sold, cancelled
  isActive        Boolean     @default(true)
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Foreign keys
  sellerId        String
  categoryId      String
  itemTypeId      String

  // Relations
  seller          User         @relation(fields: [sellerId], references: [id])
  category        Category     @relation(fields: [categoryId], references: [id])
  itemType        ItemType     @relation(fields: [itemTypeId], references: [id])
  images          ProductImage[]
  bids            Bid[]

  @@index([sellerId])
  @@index([categoryId])
  @@index([itemTypeId])
  @@index([status])
  @@index([sellType])
  @@index([slug])
}

model ProductImage {
  id          String   @id @default(uuid())
  productId   String
  imageUrl    String
  altText     String?
  sortOrder   Int      @default(0)
  isMain      Boolean  @default(false)
  createdAt   DateTime @default(now())

  // Relations
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([sortOrder])
}

model Bid {
  id          String   @id @default(uuid())
  productId   String
  bidderId    String
  amount      Decimal  @db.Decimal(10, 2)
  isWinning   Boolean  @default(false)
  createdAt   DateTime @default(now())

  // Relations
  product     Product  @relation(fields: [productId], references: [id])
  bidder      User     @relation(fields: [bidderId], references: [id])

  @@index([productId])
  @@index([bidderId])
  @@index([createdAt])
}