import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import axios, { AxiosResponse } from "axios";
import { toaster } from "@/components/ui/toaster";
import { mockMutation } from "@/utils/axios-mock";

// Types
interface AuthResponse {
  status: boolean;
  message: string;
  data?: any;
}

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
}

interface LoginData {
  emailPhoneNumber: string;
  password: string;
}

// API Base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  (error) => {
    const message = error.response?.data?.message || error.message || 'An error occurred';
    return Promise.reject(new Error(message));
  }
);

export const useAuthenticatedApi = () => {
  const { data: session } = useSession();

  const authenticatedClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
  });

  // Request interceptor to add auth header
  authenticatedClient.interceptors.request.use(
    (config) => {
      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  authenticatedClient.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    async (error) => {
      const originalRequest = error.config;

      // Handle 401 errors (token expired)
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        // If refresh fails, redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        return Promise.reject(new Error('Authentication failed'));
      }

      const message = error.response?.data?.message || error.message || 'An error occurred';
      return Promise.reject(new Error(message));
    }
  );

  return authenticatedClient;
};

interface MutationRegister {
  onError: (error: unknown) => void;
  onSuccess: (data: any) => void;
}

export const MutationRegister = ({
  onError,
  onSuccess,
}: MutationRegister): UseMutationResult<any, Error, any> => {
  const url = `/auth/register`;

  const mutationFn = async (data: any) => {
    return await mockMutation(url, data, "post" );
  };

  const resMutation = useMutation({
    mutationKey: [url],
    mutationFn: mutationFn,
    onError,
    onSuccess,
  }) as UseMutationResult;

  return resMutation;
};




