'use client'

import { ProductItem } from '@/types/product'
import { Box, Button, Flex, Heading, Icon, Image, Text } from '@chakra-ui/react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { FaRegHeart } from 'react-icons/fa6'
import ProductCardImage from './ProductCardImage'

interface ProductCardProps {
    item: ProductItem
    backgroundColor?: string
    backgroundColorCardImage?: string
    containerProps?: React.ComponentProps<typeof Box>
    imageContainerProps?: React.ComponentProps<typeof Box>
    imageProps?: React.ComponentProps<typeof Image>
    bodyContainerProps?: React.ComponentProps<typeof Box>
}

const ProductCard: React.FC<ProductCardProps> = ({
    item,
    backgroundColor = '',
    backgroundColorCardImage = 'gray.100',
    containerProps = {},
    imageContainerProps = {},
    imageProps = {},
    bodyContainerProps = {},
}) => {
    const t = useTranslations()

    return (
        <Box
            as="article"
            bg={backgroundColor}
            display="flex"
            flexDirection="column"
            w="100%"
            maxW={{ base: '170px', md: '270px' }}
            maxH={{ base: 'auto', md: '400px' }}
            gap={3}
            {...containerProps}
        >
            <Link href={`/auction/${item.slug}`} draggable="false" tabIndex={-1}>
                <ProductCardImage
                    item={item}
                    backgroundColorCardImage={backgroundColorCardImage}
                    containerProps={imageContainerProps}
                    imageProps={imageProps}
                />
            </Link>

            <Box
                {...bodyContainerProps}
                px={2} w="full">
                <Link href={`/auction/${item.slug}`} draggable="false">
                    <Text
                        lineClamp={2}
                        fontSize="sm"
                        fontWeight="semibold"
                        color="gray.800"
                        _hover={{ textDecoration: 'underline' }}
                    >
                        {item.title}
                    </Text>
                </Link>

                <Flex mt={2} alignItems="center">
                    <Heading fontWeight="bold" fontSize={{ base: 'xl', md: '2xl' }} color="gray.800">
                        {item.price}
                    </Heading>
                    <Text
                        color="gray.700"
                        fontSize={{ base: 'xs', md: 'sm' }}
                        fontWeight="regular"
                        ml={2}
                    >
                        {item.bids ?? '3'} {t('bids')}
                    </Text>
                </Flex>
                <Text mt={2} fontSize="sm" fontWeight="normal" color="gray.500">
                    {item.timeLeft ?? '2d 3h 12m'}
                </Text>
            </Box>
        </Box>
    )
}

export default ProductCard