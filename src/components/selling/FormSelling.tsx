"use client"
import { Box, <PERSON>ing, HStack, Icon, RadioCard, Stack, Text, Button, RadioGroup } from '@chakra-ui/react'
import { Checkbox } from '@chakra-ui/react'
import Link from 'next/link'
import React, { useEffect } from 'react'
import { useF<PERSON>, <PERSON> } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FaArrowLeft } from 'react-icons/fa'
import FormInputField from '../ui/form/FormInputField'
import FormSelectField, { SelectOption } from '../ui/form/FormSelectField'
import FormImageUpload from '../ui/form/FormImageUpload'
import FormTextArea from '../ui/form/FormTextArea'
import FormDateTimePicker from '../ui/form/FormDateTimePicker'
import { SingleValue } from 'react-select'
import {
    useCategoriesQuery,
    useItemTypesQuery,
    useCreateProductMutation,
    CreateProductData
} from '@/services/useProductQuery'
import { toaster } from '@/components/ui/toaster'
import { formatUSD, parseUSD } from '@/utils/helpers/helper'

// interface FormStepProps { }

type TypeSell = {
    value: string;
    title: string;
}

const TypeSell: TypeSell[] = [
    {
        value: 'auction',
        title: 'Auction',
    },
    {
        value: 'buy-now',
        title: 'Buy Now',
    },
];

// Form validation schema
const formSchema = z.object({
    sellType: z.enum(['auction', 'buy-now']),
    itemName: z.string().min(1, 'Item name is required').max(255),
    categoryId: z.string().min(1, 'Category is required'),
    itemTypeId: z.string().min(1, 'Item type is required'),
    images: z.array(z.any()).min(1, 'At least one image is required'),
    priceUSD: z.number().positive('Price must be positive'),
    description: z.string().optional(),
    auctionStartDate: z.string().optional(),
    auctionEndDate: z.string().optional(),
    extendedBiddingEnabled: z.boolean(),
    extendedBiddingMinutes: z.number().int().min(1).max(60).optional(),
    extendedBiddingDuration: z.number().int().min(1).max(120).optional(),
}).refine((data) => {
    // If auction type, start and end dates are required
    if (data.sellType === 'auction') {
        return data.auctionStartDate && data.auctionEndDate;
    }
    return true;
}, {
    message: "Auction start and end dates are required for auction type",
    path: ["auctionStartDate"]
}).refine((data) => {
    // If extended bidding is enabled, minutes and duration are required
    if (data.extendedBiddingEnabled) {
        return data.extendedBiddingMinutes && data.extendedBiddingDuration;
    }
    return true;
}, {
    message: "Extended bidding settings are required when enabled",
    path: ["extendedBiddingMinutes"]
});

type FormData = z.infer<typeof formSchema>;

const FormSelling: React.FC = () => {
    const {
        control,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isSubmitting },
        reset,
        register
    } = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            sellType: 'auction',
            itemName: '',
            categoryId: '',
            itemTypeId: '',
            images: [],
            priceUSD: 0,
            description: '',
            auctionStartDate: '',
            auctionEndDate: '',
            extendedBiddingEnabled: false,
            extendedBiddingMinutes: 5,
            extendedBiddingDuration: 10
        }
    });

    // Watch form values
    const watchedSellType = watch('sellType');
    const watchedCategoryId = watch('categoryId');
    const watchedExtendedBidding = watch('extendedBiddingEnabled');

    // React Query hooks
    const { data: categories = [] } = useCategoriesQuery();
    const { data: itemTypes = [] } = useItemTypesQuery(watchedCategoryId);
    const createProductMutation = useCreateProductMutation();

    useEffect(() => {
        if (watchedCategoryId) {
            setValue('itemTypeId', '');
        }
    }, [watchedCategoryId, setValue]);

    // Convert categories to select options
    const categoryOptions: SelectOption[] = categories.map(category => ({
        value: category.id,
        label: category.name
    }));

    // Convert item types to select options
    const itemTypeOptions: SelectOption[] = itemTypes.map(itemType => ({
        value: itemType.id,
        label: itemType.name
    }));

    // Form submit handler
    const onSubmit = async (data: FormData) => {
        try {
            const submitData: CreateProductData = {
                itemName: data.itemName,
                description: data.description,
                sellType: data.sellType,
                priceUSD: data.priceUSD,
                categoryId: data.categoryId,
                itemTypeId: data.itemTypeId,
                auctionStartDate: data.auctionStartDate,
                auctionEndDate: data.auctionEndDate,
                extendedBiddingEnabled: data.extendedBiddingEnabled,
                extendedBiddingMinutes: data.extendedBiddingMinutes,
                extendedBiddingDuration: data.extendedBiddingDuration,
                images: data.images.map((img: any, index: number) => ({
                    imageUrl: img.preview,
                    altText: `Product image ${index + 1}`,
                    sortOrder: index,
                    isMain: img.isMain || index === 0
                }))
            };

            await createProductMutation.mutateAsync(submitData);

            reset()

        } catch (error) {
            console.error('Error submitting form:', error);
        }
    };

    return (
        <Box mx={"auto"} w="full">
            <HStack alignItems="center" gap={4}>
                <Link href="/">
                    <Icon as={FaArrowLeft} boxSize={4} />
                </Link>
                <Box>
                    <Heading as="div" fontWeight="bold" size="lg" color="gray.800">
                        Selling Items
                    </Heading>
                    <Text as="div" fontSize="sm" color="gray.500">
                        Create a new auction listing to sell your items.
                    </Text>
                </Box>
            </HStack>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap={6} bg="white" p={8} mt={6} borderRadius={8} boxShadow="xs">
                    <Heading as="h3" size="md" mb={6} fontWeight="bold">
                        Information Product
                    </Heading>

                    <Controller
                        name="sellType"
                        control={control}
                        render={({ field }) => (
                            <RadioCard.Root
                                name={field.name}
                                value={field.value}
                                onValueChange={({ value }) => {
                                    field.onChange(value)
                                }}
                            >
                                <RadioCard.Label fontWeight="bold" color={"gray.800"}>
                                    Type Selling  <Box as="span" color="red.500">*</Box>
                                </RadioCard.Label>
                                <RadioCard.Label mb={2} color={"gray.500"}>
                                    Please select the type of selling you want to create.
                                </RadioCard.Label>
                                <HStack align="stretch">
                                    {TypeSell.map((item) => (
                                        <RadioCard.Item key={item.value} value={item.value} >
                                            <RadioCard.ItemHiddenInput />
                                            <RadioCard.ItemControl>
                                                <RadioCard.ItemText>{item.title}</RadioCard.ItemText>
                                                <RadioCard.ItemIndicator />
                                            </RadioCard.ItemControl>
                                        </RadioCard.Item>
                                    ))}
                                </HStack>
                                {errors.sellType && (
                                    <Text color="red.500" fontSize="sm">{errors.sellType.message}</Text>
                                )}
                            </RadioCard.Root>
                        )}
                    />

                    <FormInputField
                        label="Item Name"
                        description='Enter the name of the item you want to sell.'
                        placeholder="Example: Vintage Watch"
                        required
                        errorText={errors.itemName?.message}
                        {...register("itemName", {
                            required: "Item Name is required",
                        })}
                        invalid={!!errors.itemName?.message}
                    />

                    <Controller
                        name="categoryId"
                        control={control}
                        render={({ field }) => (
                            <FormSelectField
                                label="Category"
                                required
                                placeholder="Select Category"
                                options={categoryOptions}
                                width="100%"
                                value={categoryOptions.find(opt => opt.value === field.value) || undefined}
                                onChange={(selectedOption) => {
                                    const option = selectedOption as SingleValue<SelectOption>;
                                    field.onChange(option?.value || '');
                                }}
                                errorText={errors.categoryId?.message}
                            />
                        )}
                    />

                    <Controller
                        name="itemTypeId"
                        control={control}
                        render={({ field }) => (
                            <FormSelectField
                                label="Item Type"
                                required
                                placeholder="Select Item Type"
                                options={itemTypeOptions}
                                width="100%"
                                value={itemTypeOptions.find(opt => opt.value === field.value) || undefined}
                                onChange={(selectedOption) => {
                                    const option = selectedOption as SingleValue<SelectOption>;
                                    field.onChange(option?.value || '');
                                }}
                                errorText={errors.itemTypeId?.message}
                            />
                        )}
                    />

                    <Controller
                        name="images"
                        control={control}
                        render={({ field }) => (
                            <FormImageUpload
                                label="Product Images"
                                description="Upload multiple images of your product. First image will be the main image."
                                required
                                maxFiles={10}
                                value={field.value}
                                onImagesChange={field.onChange}
                                errorText={errors.images?.message}
                            />
                        )}
                    />

                    <Controller
                        name="priceUSD"
                        control={control}
                        render={({ field }) => {
                            const displayValue = formatUSD(field.value || 0);

                            const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                                const rawValue = parseUSD(e.target.value);
                                field.onChange(rawValue);
                            };

                            return (
                                <FormInputField
                                    label="Price (USD)"
                                    description="Enter the price in US Dollars."
                                    placeholder="$0.00"
                                    required
                                    value={displayValue}
                                    onChange={handleChange}
                                    errorText={errors.priceUSD?.message}
                                />
                            );
                        }}
                    />


                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <FormTextArea
                                label="Product Description"
                                description="Provide a detailed description of your product."
                                placeholder="Describe your product in detail..."
                                rows={6}
                                maxLength={2000}
                                value={field.value || ''}
                                onChange={field.onChange}
                                errorText={errors.description?.message}
                            />
                        )}
                    />

                    {watchedSellType === 'auction' && (
                        <Stack gap={4}>
                            <Heading as="h4" size="sm" fontWeight="bold" color="gray.800">
                                Auction Settings
                            </Heading>
                            <HStack gap={4}>
                                <Controller
                                    name="auctionStartDate"
                                    control={control}
                                    render={({ field }) => (
                                        <FormDateTimePicker
                                            label="Auction Start Date"
                                            description="When should the auction start?"
                                            required
                                            value={field.value || ''}
                                            onChange={field.onChange}
                                            min={new Date().toISOString().slice(0, 16)}
                                            errorText={errors.auctionStartDate?.message}
                                        />
                                    )}
                                />

                                <Controller
                                    name="auctionEndDate"
                                    control={control}
                                    render={({ field }) => (
                                        <FormDateTimePicker
                                            label="Auction End Date"
                                            description="When should the auction end?"
                                            required
                                            value={field.value || ''}
                                            onChange={field.onChange}
                                            min={watch('auctionStartDate') || new Date().toISOString().slice(0, 16)}
                                            errorText={errors.auctionEndDate?.message}
                                        />
                                    )}
                                />
                            </HStack>
                            <Box>
                                <Controller
                                    name="extendedBiddingEnabled"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox.Root
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        >
                                            <Checkbox.HiddenInput />
                                            <Checkbox.Control />
                                            <Checkbox.Label>
                                                <Text fontWeight="bold" color="gray.800">
                                                    Enable Extended Bidding
                                                </Text>
                                            </Checkbox.Label>
                                        </Checkbox.Root>
                                    )}
                                />
                                <Text fontSize="sm" color="gray.500" mt={1}>
                                    Automatically extend auction when bids are placed near the end
                                </Text>
                            </Box>

                            {watchedExtendedBidding && (
                                <HStack gap={4}>
                                    <Controller
                                        name="extendedBiddingMinutes"
                                        control={control}
                                        render={({ field }) => (
                                            <FormInputField
                                                label="Trigger Minutes"
                                                description="Minutes before end to trigger extension"
                                                placeholder="5"
                                                type="number"
                                                value={field.value?.toString() || ''}
                                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                                errorText={errors.extendedBiddingMinutes?.message}
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="extendedBiddingDuration"
                                        control={control}
                                        render={({ field }) => (
                                            <FormInputField
                                                label="Extension Duration"
                                                description="How long to extend (minutes)"
                                                placeholder="10"
                                                type="number"
                                                value={field.value?.toString() || ''}
                                                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                                errorText={errors.extendedBiddingDuration?.message}
                                            />
                                        )}
                                    />
                                </HStack>
                            )}
                        </Stack>
                    )}

                    <Button
                        type="submit"
                        colorScheme="blue"
                        size="lg"
                        loading={isSubmitting || createProductMutation.isPending}
                        disabled={isSubmitting || createProductMutation.isPending}
                        mt={6}
                    >
                        {isSubmitting || createProductMutation.isPending ? 'Creating...' : 'Create Listing'}
                    </Button>
                </Stack>
            </form>

        </Box>
    )
}

export default FormSelling