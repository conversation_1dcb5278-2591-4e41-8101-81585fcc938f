import { NextRequest } from 'next/server';
import { withAuth } from 'next-auth/middleware';
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

const intlMiddleware = createMiddleware(routing);

// Middleware autentikasi
const authMiddleware = withAuth(
  async function middleware(request: NextRequest) {
    const response = intlMiddleware(request);

    // Tambahkan security headers
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://accounts.google.com;"
    );

    return response;
  },
  {
    pages: {
      signIn: '/auth/login', 
    },
    callbacks: {
      authorized: ({ req, token }) => {
        const { pathname } = req.nextUrl;
        const protectedRoutes = ['/profile', '/selling', '/dashboard'];

        const normalized = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, '') || '/';
        const isProtected = protectedRoutes.some((route) =>
          normalized.startsWith(route)
        );

        if (!isProtected) return true;

        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    // Jalankan middleware untuk semua halaman (biar intl bisa jalan)
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)',
  ],
};


export default authMiddleware;
