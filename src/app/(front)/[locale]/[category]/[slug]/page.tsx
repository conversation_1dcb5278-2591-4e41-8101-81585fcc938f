'use client'
import { Box, Heading, Text, VStack, Skeleton, Stack } from '@chakra-ui/react';
import { useParams } from 'next/navigation';
import ProductImageGallery from '@/components/product/ProductImageGallery';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';
import BidInfo from '@/components/product/BidInfo';
import SalesHistory from '@/components/product/SalesHistory';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { useProductBySlugQuery } from '@/services/useProductQuery';
import { formatDistanceToNow } from 'date-fns';

const ProductDetailPage = () => {
    const { locale, category, slug } = useParams();

    const {
        data: product,
        isLoading,
        error,
        isError
    } = useProductBySlugQuery(slug as string);

    // Loading state
    if (isLoading) {
        return (
            <Box>
                <ProductDetailLayout
                    leftContent={
                        <VStack gap={4} align="stretch">
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height="20px" width="300px" />
                            </Box>
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height={{ base: '300px', lg: '500px', xl: '650px' }} />
                            </Box>
                        </VStack>
                    }
                    rightContent={
                        <Stack gap={6}>
                            <Skeleton height="40px" />
                            <Skeleton height="20px" />
                            <Skeleton height="100px" />
                            <Skeleton height="200px" />
                        </Stack>
                    }
                />
            </Box>
        );
    }

    // Error state
    if (isError || !product) {
        return (
            <Box textAlign="center" py={20}>
                <Text fontSize="xl" color="red.500" mb={4}>
                    {error instanceof Error ? error.message : 'Product not found'}
                </Text>
                <Text color="gray.600">
                    The product you're looking for doesn't exist or has been removed.
                </Text>
            </Box>
        );
    }

    // Breadcrumb items
    const breadcrumbItems = [
        {
            label: 'Home',
            href: '/'
        },
        {
            label: 'Auction',
            href: '/auction'
        },
        {
            label: product.itemName,
            isCurrentPage: true
        }
    ];

    // Calculate time left for auction
    const timeLeft = product.auctionEndDate
        ? formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true })
        : null;

    // Handle bid placement
    const handlePlaceBid = () => {
        console.log('Place bid for product:', product.id);
        // TODO: Implement bid placement logic
    };

    // Handle bid history
    const handleShowBidHistory = () => {
        console.log('Show bid history for product:', product.id);
        // TODO: Implement bid history display logic
    };

    // Handle sales history
    const handleViewSalesHistory = () => {
        console.log('View sales history for product:', product.id);
        // TODO: Implement sales history view logic
    };

    // Transform API product to match ProductItem interface for existing components
    const transformedProduct = {
        id: product.id,
        slug: product.slug || '',
        title: product.itemName,
        image: product.images.find(img => img.isMain)?.imageUrl || product.images[0]?.imageUrl || '',
        images: product.images.map(img => img.imageUrl), // ProductItem expects string[] for images
        price: `$${product.priceUSD}`, // ProductItem expects string for price
        bids: product.bidCount?.toString() || '0', // ProductItem expects string for bids
        timeLeft: timeLeft || '',
    };


    const leftContent = (
       <VStack gap={4} align="stretch">
            <Box px={{ base: 0, md: 6 }}>
                <Breadcrumb items={breadcrumbItems} />
            </Box>

            <Box
                position="sticky"
                top={{ base: 4, md: 8 }}
                px={{ base: 0, md: 6 }}
            >
                <ProductImageGallery
                    item={transformedProduct}
                    boxSizeWatchList={6}
                    containerProps={{
                        height: { base: 'full', lg: '500px', xl: '650px' },
                    }}
                />
            </Box>
        </VStack>
    );

    const rightContent = (
        <>
            <VStack align="stretch" gap={6}>
                <Box>
                    <Heading size="lg" mb={4}>{product.itemName}</Heading>
                    <Text color="gray.600" mb={2}>
                        {product.category.name} • {product.itemType.name}
                    </Text>
                    <Text color="gray.500" mb={4}>
                        by {product.seller.firstName} {product.seller.lastName}
                    </Text>
                    {product.description && (
                        <Box>
                            <Heading as="h3" size="md" mb={3}>
                                Description
                            </Heading>
                            <Text fontSize="sm" color="gray.700">{product.description}</Text>
                        </Box>
                    )}
                </Box>

                <Box as="hr" borderColor="gray.200" />

                <BidInfo
                    currentBid={product.currentBid ? `$${product.currentBid}` : `$${product.priceUSD}`}
                    bidCount={product.bidCount}
                    timeLeft={timeLeft || ''}
                    endDate={product.auctionEndDate}
                    onPlaceBid={handlePlaceBid}
                    onShowBidHistory={handleShowBidHistory}
                    buttonText={product.sellType === 'auction' ? 'Place Bid' : 'Buy Now'}
                />

                <Box as="hr" borderColor="gray.200" />

                <SalesHistory
                    onLinkClick={handleViewSalesHistory}
                />
            </VStack>
        </>
    );

    return (
        <Box>
            <ProductDetailLayout
                leftContent={leftContent}
                rightContent={rightContent}
            />
        </Box>
    );
};

export default ProductDetailPage;