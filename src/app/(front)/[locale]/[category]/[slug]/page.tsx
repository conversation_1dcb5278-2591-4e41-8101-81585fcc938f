'use client'
import { Box, Heading, Text, VStack } from '@chakra-ui/react';
import products from '@/datas/product.json';
import { useParams } from 'next/navigation';
import ProductImageGallery from '@/components/product/ProductImageGallery';
import { ProductItem } from '@/types/product';
import ProductCategory from '@/components/product/ProductCategory';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';
import ProductInfo from '@/components/product/ProductInfo';
import BidInfo from '@/components/product/BidInfo';
import SalesHistory from '@/components/product/SalesHistory';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { useProductDetail } from '@/hooks/useProductDetail';

const ProductDetailPage = () => {
    const { locale, category, slug } = useParams();
    const product: ProductItem = products.find((product) => product.slug === slug) as ProductItem;

    const { breadcrumbItems, handlePlaceBid, handleShowBidHistory, handleViewSalesHistory } = useProductDetail({
        product,
        locale: locale as string,
        category: category as string,
    });

    const leftContent = (
       <VStack gap={4} align="stretch">
            <Box px={{ base: 0, md: 6 }}>
                <Breadcrumb items={breadcrumbItems} />
            </Box>

            <Box
                position="sticky"
                top={{ base: 4, md: 8 }}
                px={{ base: 0, md: 6 }}
            >
                <ProductImageGallery
                    item={product}
                    boxSizeWatchList={6}
                    containerProps={{
                        height: { base: 'full', lg: '500px', xl: '650px' },
                    }}
                />
            </Box>
        </VStack>
    );

    const rightContent = (
        <>
            <ProductInfo
                title="2013-14 Panini Flawless Patch Autographs #PA-KB Kobe Bryant Signed Patch Card (#22/25)"
                subtitle="BGS GEM MINT 9.5, Beckett 10"
                mb={6}
            />

            <BidInfo
                currentBid="$23,000"
                bidCount={35}
                timeLeft="10H 39M"
                endDate="Fri, 5/16/25, 9:00 AM"
                onPlaceBid={handlePlaceBid}
                onShowBidHistory={handleShowBidHistory}
                mb={6}
            />

            <Box as="hr" my={6} borderColor="gray.200" />

            <Box>
                <Heading as="h3" size="md" mb={3}>
                    Description
                </Heading>
                <Text fontSize="sm" mb={4}>

                    Graded 3.0 with cream to off-white pages by CGC (3702096001). This copy is amongst the scant 78 Universal examples in CGC's census reporting. Having sold more comic books throughout his publication history than any American comic book character, Superman's influence on superheroes and comic books cannot be denied or understated. His popularity proved to publishers that superheroes were a viable subject; without the rise of Superman, we may not have the rich comic landscape that dominates our popular culture today.
                </Text>
            </Box>

            <Box as="hr" my={6} borderColor="gray.200" />

            <SalesHistory
                onLinkClick={handleViewSalesHistory}
            />
        </>
    );

    return (
        <Box>
            <Box>
                <ProductDetailLayout
                    leftContent={leftContent}
                    rightContent={rightContent}
                />
            </Box>

            <ProductCategory
                items={products}
                title="Related Items"
            />

            <ProductCategory
                items={products}
                title="Similar Items"
            />
        </Box>
    );
};

export default ProductDetailPage;