'use client'
import ProductCategory from "@/components/product/ProductCategory"
import { Box, Skeleton, Text } from "@chakra-ui/react"
import dynamic from "next/dynamic"
import React from 'react'
import { useCategoriesQuery, useProductsQuery } from '@/services/useProductQuery'
import { ProductItem } from "@/types/product"

const HeroSlider = dynamic(() => import('@/components/slider/HeroSlider'))

// Helper function to convert API product to ProductItem
const convertToProductItem = (product: any): ProductItem => {
  const mainImage = product.images?.find((img: any) => img.isMain) || product.images?.[0];

  return {
    id: product.id,
    image: mainImage?.imageUrl || '/assets/images/placeholder.png',
    images: product.images?.map((img: any) => img.imageUrl) || [],
    title: product.itemName,
    price: `$${product.priceUSD}`,
    bids: product.bidCount?.toString() || '0',
    slug: product.id,
    timeLeft: product.auctionEndDate ?
      new Date(product.auctionEndDate) > new Date() ?
        `${Math.ceil((new Date(product.auctionEndDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}d left`
        : 'Ended'
      : undefined,
  };
};

export default function HomePage() {
  const { data: categories, isLoading: categoriesLoading, error: categoriesError } = useCategoriesQuery();

  const sliderImages = [
    '/assets/images/banner-1.png',
    '/assets/images/banner-2.png',
  ];

  if (categoriesLoading) {
    return (
      <Box>
        <Box bg="white" py={4}>
          <HeroSlider images={sliderImages} />
        </Box>

        <Box p={6}>
          <Skeleton height="40px" mb={4} />
          <Skeleton height="300px" mb={6} />
          <Skeleton height="40px" mb={4} />
          <Skeleton height="300px" />
        </Box>
      </Box>
    );
  }

  if (categoriesError) {
    return (
      <Box>
        <Box bg="white" py={4}>
          <HeroSlider images={sliderImages} />
        </Box>

        <Box p={6}>
          <Box bg="red.50" border="1px solid" borderColor="red.200" borderRadius="md" p={4}>
            <Text color="red.600" fontWeight="bold" mb={2}>
              Error loading categories!
            </Text>
            <Text color="red.500" fontSize="sm">
              Failed to load product categories. Please try again later.
            </Text>
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      <Box bg="white" py={4}>
        <HeroSlider images={sliderImages} />
      </Box>

      {/* Render categories with their products */}
      {categories?.slice(0, 4).map((category) => (
        <CategorySection key={category.id} category={category} />
      ))}
    </Box>
  );
}

// Component for each category section
const CategorySection: React.FC<{ category: any }> = ({ category }) => {
  const { data: products, isLoading, error } = useProductsQuery({
    categoryId: category.id,
    limit: 10,
    status: 'active',
  });

  if (isLoading) {
    return (
      <Box p={6}>
        <Skeleton height="40px" mb={4} />
        <Skeleton height="300px" />
      </Box>
    );
  }

  if (error || !products?.products?.length) {
    return null; // Don't show empty categories
  }

  const productItems: ProductItem[] = products.products.map(convertToProductItem);

  return (
    <ProductCategory
      items={productItems}
      title={category.name}
      viewAllHref={`/marketplace?category=${category.id}`}
    />
  );
};